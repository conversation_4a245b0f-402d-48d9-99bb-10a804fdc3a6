"""
PANews数据客户端 - 集成CrawlAdapter的SimpleProxyClient
专注于新闻爬取，与Nautilus Trader无缝集成
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin
import aiohttp
import requests
import json

from nautilus_trader.live.data_client import LiveMarketDataClient
from nautilus_trader.model.identifiers import ClientId, Venue
from nautilus_trader.core.data import Data
from nautilus_trader.model.custom import customdataclass
from nautilus_trader.model.data import CustomData, DataType
from nautilus_trader.config import NautilusConfig
from nautilus_trader.live.config import LiveDataClientConfig
from nautilus_trader.common.providers import InstrumentProvider
from nautilus_trader.data.messages import SubscribeData, UnsubscribeData
from dataclasses import dataclass, field

# 导入CrawlAdapter的SimpleProxyClient
try:
    from crawladapter import SimpleProxyClient
    CRAWLADAPTER_AVAILABLE = True
except ImportError:
    CRAWLADAPTER_AVAILABLE = False
    SimpleProxyClient = None


@customdataclass
class PANewsData(Data):
    """PANews新闻数据类型"""
    title: str
    content: str
    url: str
    publish_time: str
    symbols: str = ""  # 逗号分隔的相关交易品种
    category: str = "news"
    source: str = "PANews"
    news_id: str = ""
    
    def get_symbols_list(self) -> List[str]:
        """获取交易品种列表"""
        if not self.symbols:
            return []
        return [s.strip() for s in self.symbols.split(',') if s.strip()]
    
    def is_crypto_related(self) -> bool:
        """判断是否为加密货币相关新闻"""
        crypto_keywords = [
            'bitcoin', 'btc', 'ethereum', 'eth', 'crypto', 'blockchain',
            '比特币', '以太坊', '加密货币', '区块链', 'defi', 'nft'
        ]
        text = (self.title + ' ' + self.content).lower()
        return any(keyword in text for keyword in crypto_keywords)


class PANewsDataClientConfig(LiveDataClientConfig, frozen=True):
    """PANews数据客户端配置"""
    # 基础配置
    base_url: str = "https://www.panewslab.com"  # 恢复使用HTTPS
    api_endpoint: str = "/webapi/flashnews"
    scraping_interval: int = 300  # 5分钟，用户可设置
    request_timeout: int = 30
    max_news_per_request: int = 5  # 只获取最新5条新闻

    # 代理配置
    enable_proxy: bool = True
    proxy_rules: Optional[List[str]] = None
    custom_sources: Optional[Dict] = None
    clash_config_dir: str = "./clash_configs"
    clash_binary_path: Optional[str] = None  # 用户可设置Clash二进制文件路径
    proxy_port: int = 7890  # 用户可设置代理端口
    api_port: int = 9090  # 用户可设置API端口
    enable_rules: bool = True  # 用户可设置是否启用规则

    # 请求配置
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

    def __post_init__(self):
        if self.proxy_rules is None:
            # 使用msgspec.structs.force_setattr来修改frozen对象
            import msgspec.structs
            msgspec.structs.force_setattr(self, 'proxy_rules', [
                # 目标网站
                "*.panewslab.com",
                "panewslab.com",
                # 健康检查和IP检测网站
                "*.httpbin.org",
                "*.ipinfo.io",
                "*.gstatic.com",  # Google健康检查
                "*.google.com",   # Google服务
                "*.cloudflare.com",  # Cloudflare测试
                "*.github.com",   # GitHub连通性测试
                # 常用连通性测试网站
                "connectivitycheck.gstatic.com",
                "www.gstatic.com",
                "clients3.google.com"
            ])


class PANewsDataClient(LiveMarketDataClient):
    """
    PANews数据客户端 - 集成SimpleProxyClient
    
    专注于新闻爬取功能，通过SimpleProxyClient处理代理管理
    """
    
    def __init__(
        self,
        loop: asyncio.AbstractEventLoop,
        client_id: ClientId,
        venue: Venue,
        msgbus,
        cache,
        clock,
        config: PANewsDataClientConfig,
        instrument_provider: Optional[InstrumentProvider] = None,
        name: Optional[str] = None,
    ):
        # 创建一个假的instrument_provider，因为新闻数据不需要交易品种信息
        if instrument_provider is None:
            instrument_provider = InstrumentProvider()

        super().__init__(
            loop=loop,
            client_id=client_id,
            venue=venue,
            msgbus=msgbus,
            cache=cache,
            clock=clock,
            instrument_provider=instrument_provider,
            config=config,
        )
        
        self._config = config
        self._name = name or "PANewsDataClient"
        
        # 组件初始化
        self._proxy_client: Optional[SimpleProxyClient] = None
        self._session: Optional[aiohttp.ClientSession] = None
        self._scraping_task: Optional[asyncio.Task] = None
        
        # 状态管理
        self._processed_news_ids: set = set()
        self._last_scrape_time: Optional[datetime] = None
        
        # 请求头
        self._headers = {
            'User-Agent': self._config.user_agent,
            'Accept': 'application/json, text/html, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
        
        self._log.info(f"初始化 {self._name}")
    
    async def _connect(self):
        """连接数据源"""
        self._log.info("连接PANews数据源...")

        # 初始化代理客户端（不抛出异常，即使失败也继续）
        if self._config.enable_proxy and CRAWLADAPTER_AVAILABLE:
            try:
                await self._setup_proxy_client()
            except Exception as e:
                self._log.warning(f"代理设置失败，将使用直连模式: {e}")
                self._proxy_client = None
        else:
            if not CRAWLADAPTER_AVAILABLE:
                self._log.warning("CrawlAdapter不可用，使用直连模式")
            else:
                self._log.info("代理已禁用，使用直连模式")

        # 创建HTTP会话（配置SSL以匹配成功的requests示例）
        timeout = aiohttp.ClientTimeout(total=self._config.request_timeout)
        # 创建SSL上下文，禁用证书验证（类似requests的verify=False）
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        connector = aiohttp.TCPConnector(ssl=ssl_context)
        self._session = aiohttp.ClientSession(
            timeout=timeout,
            headers=self._headers,
            connector=connector
        )

        # 启动定期爬取任务
        self._scraping_task = self._loop.create_task(self._scraping_loop())

        self._log.info("✅ PANews数据源连接成功")
    
    async def _disconnect(self):
        """断开连接"""
        self._log.info("断开PANews数据源连接...")
        
        try:
            # 停止爬取任务
            if self._scraping_task and not self._scraping_task.done():
                self._scraping_task.cancel()
                try:
                    await self._scraping_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭HTTP会话
            if self._session:
                await self._session.close()
                self._session = None
            
            # 停止代理客户端
            if self._proxy_client:
                await self._proxy_client.stop()
                self._proxy_client = None
            
            self._log.info("✅ PANews数据源断开连接")
            
        except Exception as e:
            self._log.error(f"断开PANews数据源连接时出错: {e}")
    
    async def _setup_proxy_client(self):
        """设置代理客户端"""
        try:
            self._log.info("初始化SimpleProxyClient...")

            # 准备SimpleProxyClient参数 - 使用用户配置
            proxy_params = {
                'config_dir': self._config.clash_config_dir,
                'proxy_port': self._config.proxy_port,
                'api_port': self._config.api_port,
                'enable_rules': self._config.enable_rules,
            }

            # 添加Clash二进制文件路径（如果用户设置了）
            if self._config.clash_binary_path:
                proxy_params['clash_binary_path'] = self._config.clash_binary_path
                self._log.info(f"使用自定义Clash二进制路径: {self._config.clash_binary_path}")

            # 添加自定义源（如果有）
            if self._config.custom_sources:
                proxy_params['custom_sources'] = self._config.custom_sources
                self._log.info(f"使用自定义代理源: {list(self._config.custom_sources.keys())}")
            else:
                self._log.info("使用默认代理源")

            self._log.info(f"代理配置: 端口={self._config.proxy_port}, API端口={self._config.api_port}, 启用规则={self._config.enable_rules}")

            # 创建SimpleProxyClient
            self._proxy_client = SimpleProxyClient(**proxy_params)
            
            # 启动代理客户端，使用扩展的规则和健康检查配置
            startup_rules = self._config.proxy_rules.copy()

            # 确保PANews域名在规则中（重要！）
            panews_domains = [
                "*.panewslab.com",
                "panewslab.com",
                "www.panewslab.com"
            ]

            for domain in panews_domains:
                if domain not in startup_rules:
                    startup_rules.append(domain)
                    self._log.info(f"添加PANews域名到代理规则: {domain}")

            # 确保健康检查URL在规则中
            health_check_domains = [
                "*.gstatic.com",
                "www.gstatic.com",
                "connectivitycheck.gstatic.com",
                "*.google.com",
                "*.httpbin.org"
            ]

            for domain in health_check_domains:
                if domain not in startup_rules:
                    startup_rules.append(domain)

            self._log.info(f"启动代理客户端，规则数量: {len(startup_rules)}")
            self._log.info(f"代理规则: {startup_rules}")  # 改为info级别以便查看

            success = await self._proxy_client.start(rules=startup_rules)

            if success:
                self._log.info("✅ SimpleProxyClient启动成功")

                # 获取代理状态
                try:
                    status = await self._proxy_client.get_status()
                    healthy_proxies = status.get('healthy_proxies', 0)
                    total_proxies = status.get('total_proxies', 0)

                    self._log.info(f"代理状态: 总计{total_proxies}个节点，健康{healthy_proxies}个")

                    if healthy_proxies > 0:
                        self._log.info("✅ 发现可用代理节点")
                        # 测试代理连通性
                        await self._test_proxy_connectivity()
                    else:
                        self._log.warning("⚠️ 没有健康的代理节点，将使用直连模式")

                except Exception as e:
                    self._log.warning(f"获取代理状态失败: {e}")

            else:
                self._log.warning("SimpleProxyClient启动失败，使用直连模式")
                self._proxy_client = None
                
        except Exception as e:
            self._log.error(f"设置代理客户端失败: {e}")
            self._proxy_client = None

    async def _test_proxy_connectivity(self):
        """测试代理连通性"""
        if not self._proxy_client:
            return

        try:
            # 测试多个连通性检查URL
            test_urls = [
                "http://www.gstatic.com/generate_204",
                "https://httpbin.org/ip",
                "https://www.panewslab.com"
            ]

            for test_url in test_urls:
                try:
                    proxy_url = await self._proxy_client.get_proxy(test_url)
                    if proxy_url:
                        self._log.info(f"✅ 代理连通性测试成功: {test_url}")

                        # 测试实际HTTP请求（创建临时session以避免依赖问题）
                        import ssl
                        ssl_context = ssl.create_default_context()
                        ssl_context.check_hostname = False
                        ssl_context.verify_mode = ssl.CERT_NONE
                        connector = aiohttp.TCPConnector(ssl=ssl_context)
                        async with aiohttp.ClientSession(connector=connector) as test_session:
                            async with test_session.get(test_url, proxy=proxy_url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                                if response.status == 200:
                                    self._log.info(f"✅ 代理HTTP请求成功: {test_url} -> {response.status}")
                                else:
                                    self._log.warning(f"⚠️ 代理HTTP请求异常: {test_url} -> {response.status}")
                        break  # 成功一个就够了
                    else:
                        self._log.warning(f"⚠️ 无法获取代理URL: {test_url}")

                except Exception as e:
                    self._log.warning(f"⚠️ 代理连通性测试失败: {test_url} -> {e}")
                    continue

        except Exception as e:
            self._log.warning(f"代理连通性测试异常: {e}")

    async def _scraping_loop(self):
        """定期爬取新闻的主循环"""
        self._log.info("启动新闻爬取循环")

        while True:
            try:
                await self._scrape_and_publish_news()
                await asyncio.sleep(self._config.scraping_interval)

            except asyncio.CancelledError:
                self._log.info("新闻爬取循环被取消")
                break
            except Exception as e:
                self._log.error(f"新闻爬取循环异常: {e}")
                # 出错后等待较短时间再重试
                await asyncio.sleep(60)

    async def _scrape_and_publish_news(self):
        """爬取并发布新闻"""
        try:
            self._log.info("开始爬取PANews...")

            # 构建API URL
            api_url = urljoin(
                self._config.base_url,
                f"{self._config.api_endpoint}?LId=1&Rn={self._config.max_news_per_request}&tw=0"
            )

            # 获取代理URL
            proxy_url = await self._get_proxy_url(api_url)

            if proxy_url:
                self._log.info(f"使用代理: {proxy_url}")
                # 额外测试：确认代理客户端是否正确处理PANews域名
                test_panews_url = "https://www.panewslab.com"
                test_proxy = await self._get_proxy_url(test_panews_url)
                if test_proxy:
                    self._log.info(f"✅ PANews域名代理确认: {test_proxy}")
                else:
                    self._log.warning("⚠️ PANews域名未被代理处理，可能导致连接失败")
            else:
                self._log.info("使用直连")

            # 发起请求（带重试逻辑）
            news_items = []
            max_retries = 3

            for attempt in range(max_retries):
                try:
                    # 使用requests替代aiohttp
                    # 准备requests的代理配置
                    proxies = None
                    if proxy_url:
                        proxies = {
                            'http': proxy_url,
                            'https': proxy_url
                        }

                    # 准备headers
                    headers = {
                        'User-Agent': self._config.user_agent,
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Referer': self._config.base_url,
                    }

                    # 使用requests.get，类似你的成功示例
                    response = requests.get(
                        api_url,
                        proxies=proxies,
                        headers=headers,
                        timeout=self._config.request_timeout,
                        verify=False  # 禁用SSL验证，类似你的成功示例
                    )

                    if response.status_code == 200:
                        # 解析JSON响应
                        json_data = response.json()
                        news_items = self._parse_news_json(json_data)
                        break
                    elif response.status_code == 502 and attempt < max_retries - 1:
                        self._log.warning(f"代理请求失败 (HTTP {response.status_code})，尝试直连 (尝试 {attempt + 1}/{max_retries})")
                        # 如果代理失败，尝试直连
                        if attempt == 1:
                            proxy_url = None
                        continue
                    else:
                        self._log.error(f"请求失败: HTTP {response.status_code}")
                        return
                except Exception as e:
                    if attempt < max_retries - 1:
                        self._log.warning(f"请求异常，重试中 (尝试 {attempt + 1}/{max_retries}): {e}")
                        # 第二次尝试使用直连
                        if attempt == 1:
                            proxy_url = None
                        await asyncio.sleep(5)  # 等待5秒后重试
                        continue
                    else:
                        self._log.error(f"请求最终失败: {e}")
                        return
            else:
                self._log.error("所有重试都失败了")
                return

            # 过滤新新闻并发布
            new_items = []
            for item in news_items:
                if item.news_id not in self._processed_news_ids:
                    new_items.append(item)
                    self._processed_news_ids.add(item.news_id)

            # 发布新闻数据为CustomData
            for item in new_items:
                # 创建CustomData包装新闻数据
                custom_data = CustomData(
                    data_type=DataType(PANewsData, metadata={"source": "PANews"}),
                    data=item
                )
                self._handle_data(custom_data)

            self._log.info(f"爬取完成: 总计{len(news_items)}条，新增{len(new_items)}条")
            self._last_scrape_time = datetime.now()

        except Exception as e:
            self._log.error(f"爬取新闻失败: {e}")

    async def _get_proxy_url(self, target_url: str) -> Optional[str]:
        """获取代理URL"""
        if not self._proxy_client:
            return None

        try:
            return await self._proxy_client.get_proxy(target_url)
        except Exception as e:
            self._log.warning(f"获取代理URL失败: {e}")
            return None

    def _parse_news_json(self, json_data: dict) -> List[PANewsData]:
        """解析JSON数据为新闻对象"""
        news_items = []

        try:
            # 解析PANews API的JSON结构
            if not json_data or 'data' not in json_data:
                self._log.warning("无效的JSON结构: 缺少'data'字段")
                return []

            data = json_data['data']
            if not isinstance(data, dict) or 'flashNews' not in data:
                self._log.warning("无效的JSON结构: 缺少'flashNews'字段")
                return []

            flash_news = data['flashNews']
            if not isinstance(flash_news, list) or len(flash_news) == 0:
                self._log.warning("无效的JSON结构: 'flashNews'不是有效列表")
                return []

            # 获取新闻列表
            first_flash_news = flash_news[0]
            if not isinstance(first_flash_news, dict) or 'list' not in first_flash_news:
                self._log.warning("无效的JSON结构: 缺少'list'字段")
                return []

            news_list = first_flash_news['list']
            if not isinstance(news_list, list):
                self._log.warning("无效的JSON结构: 'list'不是有效列表")
                return []

            # 解析每条新闻
            for item in news_list:
                try:
                    news_item = self._parse_single_news_item(item)
                    if news_item:
                        news_items.append(news_item)
                except Exception as e:
                    self._log.warning(f"解析单条新闻失败: {e}")
                    continue

        except Exception as e:
            self._log.error(f"解析JSON数据失败: {e}")

        return news_items

    def _parse_single_news_item(self, item: dict) -> Optional[PANewsData]:
        """解析单条新闻项"""
        try:
            # 提取基本信息
            title = item.get('title', '').strip()
            if not title or len(title) < 3:
                return None

            content = item.get('desc', '').replace('\\r\\n原文链接', '').replace('\\\\r\\\\n', '\n')
            news_id = str(item.get('id', ''))

            # 处理发布时间
            publish_time_timestamp = item.get('publishTime', 0)
            if publish_time_timestamp:
                dt = datetime.fromtimestamp(publish_time_timestamp)
                publish_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                publish_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 构建URL
            item_type = item.get('type', 0)
            if news_id:
                if item_type == 2:  # 快讯类型
                    url = urljoin(self._config.base_url, f"/zh/newsflash/{news_id}")
                else:
                    url = urljoin(self._config.base_url, f"/zh/articledetails/{news_id}.html")
            else:
                url = ""

            # 提取分类信息
            tags = item.get('tags')
            if isinstance(tags, list) and tags:
                category = ', '.join(str(tag) for tag in tags)
            elif tags:
                category = str(tags)
            else:
                category = 'flashnews'

            # 提取作者信息
            author_info = item.get('author', {})
            if isinstance(author_info, dict):
                author_name = author_info.get('name', '')
                if author_name:
                    category = f"{category} | {author_name}"

            # 提取交易品种符号
            symbols = self._extract_crypto_symbols(f"{title} {content}")
            symbols_str = ','.join(symbols) if symbols else ""

            # 创建新闻数据对象
            return PANewsData(
                title=title,
                content=content,
                url=url,
                publish_time=publish_time,
                symbols=symbols_str,
                category=category,
                source="PANews",
                news_id=news_id,
                ts_event=self._clock.timestamp_ns(),
                ts_init=self._clock.timestamp_ns()
            )

        except Exception as e:
            self._log.warning(f"解析新闻项失败: {e}")
            return None

    def _extract_crypto_symbols(self, text: str) -> List[str]:
        """从文本中提取加密货币符号"""
        if not text:
            return []

        # 常见加密货币符号
        crypto_symbols = [
            'BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'XRP', 'DOT', 'DOGE',
            'AVAX', 'SHIB', 'MATIC', 'LTC', 'UNI', 'LINK', 'ATOM',
            'USDT', 'USDC', 'BUSD', 'DAI', 'WBTC', 'AAVE', 'MKR',
            'COMP', 'YFI', 'SUSHI', 'CRV', 'SNX', 'BAL', 'REN'
        ]

        text_upper = text.upper()
        found_symbols = []

        for symbol in crypto_symbols:
            # 使用单词边界匹配，避免误匹配
            import re
            pattern = r'\b' + re.escape(symbol) + r'\b'
            if re.search(pattern, text_upper):
                found_symbols.append(symbol)

        return list(set(found_symbols))  # 去重

    async def get_proxy_status(self) -> Dict[str, Any]:
        """获取代理状态信息"""
        if not self._proxy_client:
            return {
                'proxy_enabled': False,
                'status': 'disabled'
            }

        try:
            status = await self._proxy_client.get_status()
            return {
                'proxy_enabled': True,
                'proxy_client_status': status,
                'last_scrape_time': self._last_scrape_time.isoformat() if self._last_scrape_time else None,
                'processed_news_count': len(self._processed_news_ids)
            }
        except Exception as e:
            return {
                'proxy_enabled': True,
                'status': 'error',
                'error': str(e)
            }

    async def switch_proxy(self) -> bool:
        """切换代理"""
        if not self._proxy_client:
            self._log.warning("代理客户端不可用")
            return False

        try:
            return await self._proxy_client.switch_proxy()
        except Exception as e:
            self._log.error(f"切换代理失败: {e}")
            return False

    async def manual_scrape(self) -> List[PANewsData]:
        """手动触发一次新闻爬取"""
        try:
            self._log.info("手动触发新闻爬取")

            # 构建API URL
            api_url = urljoin(
                self._config.base_url,
                f"{self._config.api_endpoint}?LId=1&Rn={self._config.max_news_per_request}&tw=0"
            )

            # 获取代理URL
            proxy_url = await self._get_proxy_url(api_url)

            # 发起请求
            async with self._session.get(api_url, proxy=proxy_url) as response:
                if response.status != 200:
                    self._log.error(f"手动爬取失败: HTTP {response.status}")
                    return []

                # 解析JSON响应
                json_data = await response.json()
                news_items = self._parse_news_json(json_data)

                self._log.info(f"手动爬取完成: 获取{len(news_items)}条新闻")
                return news_items

        except Exception as e:
            self._log.error(f"手动爬取失败: {e}")
            return []

    # -- SUBSCRIPTIONS ----------------------------------------------------------------------------

    async def _subscribe(self, command: SubscribeData) -> None:
        """处理数据订阅"""
        self._log.info(f"订阅数据类型: {command.data_type}")

        # 对于新闻数据，我们不需要特殊的订阅逻辑
        # 因为新闻数据是通过定期爬取任务自动获取的
        # 这里只需要记录订阅即可
        if command.data_type.type == PANewsData:
            self._log.info("✅ 已订阅PANews新闻数据")
        else:
            self._log.warning(f"不支持的数据类型: {command.data_type}")

    async def _unsubscribe(self, command: UnsubscribeData) -> None:
        """处理数据取消订阅"""
        self._log.info(f"取消订阅数据类型: {command.data_type}")

        if command.data_type.type == PANewsData:
            self._log.info("✅ 已取消订阅PANews新闻数据")
        else:
            self._log.warning(f"不支持的数据类型: {command.data_type}")

    async def _request(self, request) -> None:
        """处理数据请求"""
        self._log.warning(f"PANewsDataClient不支持数据请求，只支持订阅模式: {request}")


# 工厂类
from nautilus_trader.live.factories import LiveDataClientFactory


class PANewsDataClientFactory(LiveDataClientFactory):
    """PANews数据客户端工厂"""

    @staticmethod
    def create(
        loop: asyncio.AbstractEventLoop,
        name: str,
        config: PANewsDataClientConfig,
        msgbus,
        cache,
        clock,
    ) -> PANewsDataClient:
        """创建PANews数据客户端"""

        client_id = ClientId(name)
        venue = Venue("PANEWS")

        # 创建假的instrument_provider，新闻数据不需要交易品种信息
        instrument_provider = InstrumentProvider()

        return PANewsDataClient(
            loop=loop,
            client_id=client_id,
            venue=venue,
            msgbus=msgbus,
            cache=cache,
            clock=clock,
            config=config,
            instrument_provider=instrument_provider,
            name=name
        )
