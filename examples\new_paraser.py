import asyncio
import csv
import re
import sys
import traceback
import random
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass, asdict

from playwright.async_api import async_playwright, <PERSON>, Browser


@dataclass
class NewsItem:
    """新闻项数据结构"""
    title: str
    content: str
    time: str  
    date: str
    url: str
    is_featured: bool = False
    scraped_at: str = ""
    raw_datetime: Optional[datetime] = None  # 新增：完整的日期时间对象

    def __post_init__(self):
        """设置爬取时间戳"""
        if not self.scraped_at:
            self.scraped_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


class PANewsScraper:
    """PANews新闻爬虫 - 修复日期过滤和排序版"""
    
    BASE_URL = "https://www.panewslab.com/zh/newsflash"
    DEFAULT_TIMEOUT = 30000    
    DEFAULT_USER_AGENT = (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/********* Safari/537.36"
    )
    def __init__(
        self, 
        headless: bool = True, 
        timeout: int = None, 
        performance_mode: str = "balanced", 
        use_random_ua: bool = False, 
        mobile: bool = False
    ):
        """
        初始化爬虫
        
        Args:
            headless: 是否无头模式
            timeout: 超时时间
            performance_mode: 性能模式 
                - "fast": 最快速度，禁用图片和CSS
                - "balanced": 平衡模式，正常加载
                - "compatible": 兼容模式，最大兼容性
            use_random_ua: 是否使用随机User-Agent
            mobile: 是否使用移动端User-Agent
        """
        self.headless = headless
        self.timeout = timeout or self.DEFAULT_TIMEOUT
        self.performance_mode = performance_mode
        self.use_random_ua = use_random_ua
        self.mobile = mobile
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self._processed_urls: Set[str] = set()
        self._target_date: Optional[datetime] = None  # 新增：目标日期
        
        # 选择User-Agent
        if mobile:
            self.user_agent = self.get_mobile_user_agent()
        elif use_random_ua:
            self.user_agent = self.get_random_user_agent()
        else:
            self.user_agent = self.DEFAULT_USER_AGENT
    
    def _get_browser_args(self) -> List[str]:
        """根据配置获取浏览器启动参数"""
        base_args = [
            # 基础安全和性能参数
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-extensions',
            '--disable-gpu',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            
            # 内存和性能优化
            '--memory-pressure-off',
            '--max_old_space_size=4096',
            '--disable-ipc-flooding-protection',
            
            # 网络和加载优化
            '--disable-features=TranslateUI',
            '--disable-features=VizDisplayCompositor',
            '--disable-background-networking',
            '--disable-default-apps',
            '--disable-sync',
            
            # 隐私和追踪保护
            '--disable-web-security',
            '--disable-blink-features=AutomationControlled',
            
            # Window管理
            '--window-size=1920,1080',
            '--virtual-time-budget=5000',
        ]
        
        # 根据性能模式添加额外参数
        if self.performance_mode == "fast":
            base_args.extend([
                '--disable-images',
                '--disable-javascript',
                '--disable-plugins',
                '--disable-audio-output',
                '--disable-background-media-processing',
                '--aggressive-cache-discard',
            ])
        elif self.performance_mode == "balanced":
            base_args.extend([
                '--disable-audio-output',
                '--disable-background-media-processing',
                '--disable-canvas-aa',
                '--disable-2d-canvas-clip-aa',
            ])
        # compatible模式使用最少的参数以确保兼容性
        
        # headless模式的额外优化
        if self.headless:
            base_args.extend([
                '--disable-software-rasterizer',
                '--disable-threaded-animation',
                '--disable-threaded-scrolling',
                '--disable-partial-raster',                '--disable-skia-runtime-opts',
                '--run-all-compositor-stages-before-draw',
                '--disable-new-content-rendering-timeout',
            ])
        return base_args
    
    async def __aenter__(self):
        await self._start_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._close_browser()
        
    async def _start_browser(self):
        self.playwright = await async_playwright().start()
        
        # 简化的浏览器启动参数，避免headless模式冲突
        safe_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-extensions',
            # 强制时区设置相关参数
            '--disable-backgrounding-occluded-windows',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
        ]
        
        try:
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=safe_args
            )
        except Exception as e:
            print(f"❌ 浏览器启动失败，尝试基础配置: {e}")
            # 如果失败，使用最基础的配置
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
          # 创建浏览器上下文，设置时区为UTC+8 (Asia/Shanghai)
        self.context = await self.browser.new_context(
            timezone_id='Asia/Shanghai',  # 设置时区为UTC+8
            locale='zh-CN',  # 设置语言环境为中文
            viewport={'width': 1920, 'height': 1080},  # 设置视口大小
            user_agent=self.user_agent,  # 直接设置用户代理
            extra_http_headers={
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',  # 设置接受语言
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            },
            # 地理位置设置为中国北京
            geolocation={'latitude': 39.9042, 'longitude': 116.4074},  # 北京坐标
            permissions=['geolocation'],
        )        # 从上下文创建页面
        self.page = await self.context.new_page()
        self.page.set_default_timeout(self.timeout)
        
        # 注入时区脚本
        await self._inject_timezone_script(self.page)
        
        print("🕒 已强制设置浏览器时区为 UTC+8 (Asia/Shanghai)")

    async def _inject_timezone_script(self, page):
        """为页面注入时区脚本，确保使用UTC+8时区"""
        await page.add_init_script("""
            // 强制覆盖时区相关的JavaScript对象
            Object.defineProperty(Date.prototype, 'getTimezoneOffset', {
                value: function() { return -480; },  // UTC+8 对应 -480 分钟
                writable: false,
                configurable: false
            });
            
            // 覆盖 Intl.DateTimeFormat 的时区设置
            const originalDateTimeFormat = Intl.DateTimeFormat;
            Intl.DateTimeFormat = function(locale, options) {
                if (options) {
                    options.timeZone = 'Asia/Shanghai';
                } else {
                    options = { timeZone: 'Asia/Shanghai' };
                }
                return new originalDateTimeFormat(locale || 'zh-CN', options);
            };
            
            // 设置默认时区
            Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {
                value: function() {
                    const original = originalDateTimeFormat.prototype.resolvedOptions.call(this);
                    original.timeZone = 'Asia/Shanghai';
                    original.locale = 'zh-CN';
                    return original;
                },
                writable: false
            });
            
            // 强制设置navigator.timezone
            Object.defineProperty(navigator, 'timezone', {
                value: 'Asia/Shanghai',
                writable: false,
                configurable: false
            });
            
            // 覆盖Date构造函数以确保时区一致性
            const OriginalDate = Date;
            Date = function(...args) {
                if (args.length === 0) {
                    // 当前时间，确保使用正确时区
                    return new OriginalDate();
                }
                return new OriginalDate(...args);
            };
            Date.prototype = OriginalDate.prototype;
            Date.now = OriginalDate.now;
            Date.parse = OriginalDate.parse;
            Date.UTC = OriginalDate.UTC;
            
            console.log('✅ UTC+8 时区脚本已注入');
        """)

    async def _close_browser(self):
        if self.page:
            # 先关闭页面
            await self.page.close()
            self.page = None
        if hasattr(self, 'context') and self.context:
            # 关闭我们保存的上下文
            await self.context.close()
            self.context = None
        if self.browser:
            # 关闭浏览器
            await self.browser.close()
            self.browser = None
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None

    async def _save_debug_html(self, stage: str, description: str = ""):
        """保存调试HTML文件以排查问题"""
        try:
            # 创建调试目录
            debug_dir = Path("debug_html")
            debug_dir.mkdir(exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{stage}.html"
            filepath = debug_dir / filename

            # 获取页面HTML
            html_content = await self.page.content()

            # 添加调试信息到HTML顶部
            debug_info = f"""
<!-- 调试信息 -->
<!-- 时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")} -->
<!-- 阶段: {stage} -->
<!-- 描述: {description} -->
<!-- URL: {self.page.url} -->
<!-- ==================== -->

"""

            # 保存HTML文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(debug_info + html_content)

            print(f"🐛 调试HTML已保存: {filepath} - {description}")

            # 同时保存页面截图
            screenshot_path = debug_dir / f"{timestamp}_{stage}.png"
            await self.page.screenshot(path=str(screenshot_path), full_page=True)
            print(f"📸 调试截图已保存: {screenshot_path}")

        except Exception as e:
            print(f"⚠️ 保存调试HTML失败: {e}")

    async def _save_page_state_debug(self, stage: str, additional_info: dict = None):
        """保存页面状态的详细调试信息"""
        try:
            debug_dir = Path("debug_html")
            debug_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存HTML
            await self._save_debug_html(stage, f"页面状态_{stage}")

            # 保存详细的页面信息
            info_file = debug_dir / f"{timestamp}_{stage}_info.txt"

            with open(info_file, 'w', encoding='utf-8') as f:
                f.write(f"调试信息 - {stage}\n")
                f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"URL: {self.page.url}\n")
                f.write("=" * 50 + "\n\n")

                # 获取当前显示的日期信息
                try:
                    current_year, current_month = await self._get_current_displayed_date()
                    f.write(f"当前显示日期: {current_year}年{current_month}月\n")
                except:
                    f.write("无法获取当前显示日期\n")

                # 获取页面上的新闻数量
                try:
                    news_containers = await self.page.query_selector_all('div.border-brand-tertiary.border-l-4')
                    f.write(f"页面新闻容器数量: {len(news_containers)}\n")
                except:
                    f.write("无法获取新闻容器数量\n")

                # 检查日期分隔符
                try:
                    date_dividers = await self._check_date_dividers_in_new_content()
                    f.write(f"发现的日期分隔符: {date_dividers}\n")
                except:
                    f.write("无法获取日期分隔符\n")                # 添加额外信息
                if additional_info:
                    f.write("\n额外信息:\n")
                    for key, value in additional_info.items():
                        f.write(f"{key}: {value}\n")

            print(f"📋 页面状态信息已保存: {info_file}")
            
        except Exception as e:
            print(f"⚠️ 保存页面状态调试信息失败: {e}")

    async def navigate_to_newsflash(self):
        print(f"正在导航到 {self.BASE_URL}")
        await self.page.goto(self.BASE_URL)
        await self.page.wait_for_selector('button:has-text("加载更多快讯")', timeout=15000)
        print("页面加载完成")
        
        # 验证时区设置
        await self._verify_timezone_setting()

        # 保存初始页面状态
        await self._save_debug_html("initial_page_load", "初始页面加载完成")
        print("页面加载完成")
        
        # 验证时区设置
        await self._verify_timezone_setting()

        # 保存初始页面状态
        await self._save_debug_html("initial_page_load", "初始页面加载完成")
        
    async def select_specific_date(self, year: int, month: int, day: int):
        """选择具体的年月日"""
        target_date_str = f"{year}-{month:02d}-{day:02d}"
        target_display = f"{year}年{month}月{day}日"
        self._target_date = datetime(year, month, day)  # 设置目标日期

        print(f"🗓️ 开始选择日期: {target_display}")
        
        # 在日期选择前验证时区设置
        await self._verify_timezone_setting()

        # 保存日期选择开始时的页面状态
        await self._save_debug_html("date_selection_start", f"开始选择日期_{target_display}")

        try:
            # 基于新的HTML结构更新选择器
            date_button_selectors = [
                'button[id*="reka-popover-trigger"]:has-text("年")',  # 匹配reka-popover-trigger ID
                'button[aria-haspopup="dialog"]:has-text("年")',      # 匹配aria-haspopup属性
                'button.cursor-pointer:has-text("年")',               # 包含"年"字的按钮
                'button:has(.iconify--lucide)',                       # 包含日历图标的按钮
                'button:has-text("年"):has(svg)',                     # 同时包含"年"和SVG图标的按钮
                '[role="button"]:has-text("年")',                     # 备用选择器
            ]
            
            date_button = None
            for selector in date_button_selectors:
                try:
                    date_button = await self.page.query_selector(selector)
                    if date_button:
                        button_text = await date_button.text_content()
                        print(f"✅ 找到日期按钮: {selector} - 文本: '{button_text}'")
                        break
                except:
                    continue
            
            if not date_button:
                # 更智能的备用查找方法
                all_buttons = await self.page.query_selector_all('button')
                for button in all_buttons:
                    try:
                        text = await button.text_content()
                        aria_haspopup = await button.get_attribute('aria-haspopup')
                        button_id = await button.get_attribute('id')
                        
                        # 检查是否是日历按钮的特征
                        if (text and ('年' in text and '月' in text) or 
                            aria_haspopup == 'dialog' or 
                            (button_id and 'reka-popover-trigger' in button_id)):
                            date_button = button
                            print(f"✅ 找到备用日期按钮: {text} (ID: {button_id})")
                            break
                    except:
                        continue
            
            if not date_button:
                print("❌ 无法找到日期选择器，使用当前页面日期")
                return
            
            await date_button.click()
            print("📅 日历选择器已打开")
            await self.page.wait_for_timeout(2000)

            # 保存日历打开后的状态
            await self._save_debug_html("calendar_opened", f"日历打开_{target_display}")

            await self._navigate_to_target_month(year, month)
            await self._select_specific_day(year, month, day)            # 保存日期选择完成后的状态
            await self._save_debug_html("date_selection_complete", f"日期选择完成_{target_display}")

            # 日期选择完成后，重新验证和注入时区设置
            print("🕒 日期选择完成，重新验证时区设置...")
            timezone_ok = await self._verify_timezone_setting()
            if not timezone_ok:
                print("⚠️ 时区验证失败，重新注入时区脚本")
                await self._inject_timezone_script(self.page)
                await self._verify_timezone_setting()

            print(f"✅ 成功选择日期: {target_display}")
            
        except Exception as e:
            print(f"❌ 日期选择失败: {e}")
            try:
                await self.page.keyboard.press('Escape')
                await self.page.click('body')
            except:
                pass
    
    async def _navigate_to_target_month(self, target_year: int, target_month: int):
        """导航到目标年月 - 智能年份月份分离导航版"""
        try:
            # 保存导航前的HTML状态
            await self._save_debug_html("navigation_start", f"导航开始_目标{target_year}年{target_month}月")

            # 获取当前显示的年月
            current_year, current_month = await self._get_current_displayed_date()
            if current_year is None or current_month is None:
                print("❌ 无法获取当前日期，导航失败")
                await self._save_debug_html("navigation_failed", "无法获取当前日期")
                return

            print(f"📅 当前: {current_year}年{current_month}月, 目标: {target_year}年{target_month}月")

            # 检查是否已经在目标年月
            if current_year == target_year and current_month == target_month:
                print(f"✅ 已经在目标年月: {target_year}年{target_month}月")
                await self._save_debug_html("navigation_already_target", f"已在目标年月{target_year}年{target_month}月")
                return
            
            # 第一步：导航到目标年份
            if current_year != target_year:
                year_diff = target_year - current_year
                print(f"🗓️ 需要调整年份: {year_diff} 年")
                
                if year_diff > 0:
                    # 需要前进年份
                    await self._click_year_navigation('next', abs(year_diff))
                else:
                    # 需要后退年份
                    await self._click_year_navigation('prev', abs(year_diff))
                
                # 验证年份导航结果
                new_year, new_month = await self._get_current_displayed_date()
                await self._save_debug_html("year_navigation_result", f"年份导航后_当前{new_year}年{new_month}月_目标{target_year}年")

                if new_year != target_year:
                    print(f"⚠️ 年份导航可能未完全成功，当前: {new_year}年，目标: {target_year}年")
                else:
                    print(f"✅ 年份导航成功: {target_year}年")

                current_year = new_year
                current_month = new_month
            
            # 第二步：导航到目标月份
            if current_month != target_month:
                month_diff = target_month - current_month
                print(f"📅 需要调整月份: {month_diff} 个月")
                
                if month_diff > 0:
                    # 需要前进月份
                    await self._click_month_navigation('next', abs(month_diff))
                else:
                    # 需要后退月份
                    await self._click_month_navigation('prev', abs(month_diff))
                
                # 验证月份导航结果
                final_year, final_month = await self._get_current_displayed_date()
                await self._save_debug_html("month_navigation_result", f"月份导航后_当前{final_year}年{final_month}月_目标{target_year}年{target_month}月")

                if final_year == target_year and final_month == target_month:
                    print(f"✅ 月份导航成功: {target_year}年{target_month}月")
                    await self._save_debug_html("navigation_success", f"导航成功_到达{target_year}年{target_month}月")
                else:
                    print(f"⚠️ 月份导航可能未完全成功，当前: {final_year}年{final_month}月，目标: {target_year}年{target_month}月")
                    await self._save_debug_html("navigation_partial_success", f"导航部分成功_当前{final_year}年{final_month}月_目标{target_year}年{target_month}月")

        except Exception as e:
            print(f"❌ 月份导航失败: {e}")
            await self._save_debug_html("navigation_error", f"导航异常_{str(e)}")
            import traceback
            traceback.print_exc()
    
    async def _get_current_displayed_date(self) -> Tuple[Optional[int], Optional[int]]:
        """获取当前日历显示的年月"""
        try:
            # 在日历打开状态下，查找显示当前年月的元素
            month_display_selectors = [
                'button[id*="reka-popover-trigger"]',  # 主日期按钮
                '.text-center.font-medium.truncate.mx-auto.text-sm',  # 日历头部显示
                '[data-reka-calendar-header]',
                '.calendar-header',
                'h2:has-text("年")',
                'div:has-text("年")'
            ]
            
            current_month_element = None
            for selector in month_display_selectors:
                current_month_element = await self.page.query_selector(selector)
                if current_month_element:
                    current_text = await current_month_element.text_content()
                    if current_text and '年' in current_text and '月' in current_text:
                        break
            
            if not current_month_element:
                # 备用方法：查找所有包含年月的元素
                all_elements = await self.page.query_selector_all('button, div, span, h1, h2, h3')
                for element in all_elements:
                    try:
                        text = await element.text_content()
                        if text and re.search(r'\d{4}年\d+月', text):
                            current_month_element = element
                            break
                    except:
                        continue
                        
                if not current_month_element:
                    print("❌ 无法找到任何年月显示元素")
                    return None, None
            
            current_text = await current_month_element.text_content()
            print(f"🔍 找到日期显示文本: '{current_text}'")
              # 解析年月
            current_match = re.search(r'(\d{4})年(\d+)月', current_text)
            if not current_match:
                # 尝试其他格式
                alternative_match = re.search(r'(\d{4})\s*[-/年]\s*(\d+)', current_text)
                if alternative_match:
                    current_match = alternative_match
                else:
                    print(f"❌ 无法解析日期文本: {current_text}")
                    return None, None
            
            current_year = int(current_match.group(1))
            current_month = int(current_match.group(2))
            
            return current_year, current_month
            
        except Exception as e:
            print(f"❌ 获取当前显示日期失败: {e}")
            return None, None
    
    async def _find_navigation_button(self, button_type: str) -> Optional[object]:
        """统一的导航按钮查找方法 - 基于精确的HTML结构"""
        button_configs = {
            'prev_month': {
                'aria_label': '上个月',
                'svg_path': 'm15 18l-6-6l6-6',
                'description': '上个月'
            },
            'next_month': {
                'aria_label': '下个月',
                'svg_path': 'm9 18l6-6l-6-6',
                'description': '下个月'
            },
            'prev_year': {
                'aria_label': '去年',
                'svg_path': 'm11 17l-5-5l5-5m7 10l-5-5l5-5',
                'description': '去年'
            },
            'next_year': {
                'aria_label': '明年',
                'svg_path': 'm6 17l5-5l-5-5m7 10l5-5l-5-5',
                'description': '明年'
            }
        }

        if button_type not in button_configs:
            print(f"❌ 未知的按钮类型: {button_type}")
            return None

        config = button_configs[button_type]

        # 方法1: 通过aria-label直接查找（最可靠）
        try:
            button = await self.page.query_selector(f'button[aria-label="{config["aria_label"]}"]')
            if button:
                is_visible = await button.is_visible()
                is_enabled = await button.is_enabled()
                if is_visible and is_enabled:
                    print(f"✅ 通过aria-label找到{config['description']}按钮")
                    return button
                else:
                    print(f"⚠️ {config['description']}按钮不可用: visible={is_visible}, enabled={is_enabled}")
        except Exception as e:
            print(f"⚠️ aria-label查找{config['description']}按钮失败: {e}")

        # 方法2: 通过SVG路径查找
        try:
            # 查找所有SVG元素
            svg_elements = await self.page.query_selector_all('svg[viewBox="0 0 24 24"]')
            for svg in svg_elements:
                try:
                    # 查找特定路径的path元素
                    path_element = await svg.query_selector(f'path[d="{config["svg_path"]}"]')
                    if path_element:
                        # 找到SVG的父按钮
                        parent_button = await svg.evaluate('(el) => el.closest("button")')
                        if parent_button:
                            # 验证按钮状态
                            is_visible = await parent_button.is_visible()
                            is_enabled = await parent_button.is_enabled()
                            if is_visible and is_enabled:
                                print(f"✅ 通过SVG路径找到{config['description']}按钮")
                                return parent_button
                            else:
                                print(f"⚠️ SVG父按钮不可用: visible={is_visible}, enabled={is_enabled}")
                except Exception as e:
                    continue
        except Exception as e:
            print(f"⚠️ SVG路径查找{config['description']}按钮失败: {e}")

        # 方法3: 通过组合选择器查找
        try:
            combined_selectors = [
                f'button:has(svg[viewBox="0 0 24 24"]:has(path[d="{config["svg_path"]}"]))',
                f'button.rounded-md:has(svg:has(path[d="{config["svg_path"]}"]))',
                f'button[aria-label*="{config["aria_label"]}"]'
            ]

            for selector in combined_selectors:
                try:
                    button = await self.page.query_selector(selector)
                    if button:
                        is_visible = await button.is_visible()
                        is_enabled = await button.is_enabled()
                        if is_visible and is_enabled:
                            print(f"✅ 通过组合选择器找到{config['description']}按钮: {selector}")
                            return button
                except Exception as e:
                    continue
        except Exception as e:
            print(f"⚠️ 组合选择器查找{config['description']}按钮失败: {e}")

        print(f"❌ 无法找到{config['description']}按钮")
        return None

    async def _click_month_navigation(self, direction: str, times: int):
        """点击月份导航按钮 - 优化版本"""
        button_type = 'next_month' if direction == 'next' else 'prev_month'
        navigation_success = True

        for i in range(times):
            print(f"🔄 尝试点击{direction}月份按钮 (第{i+1}/{times}次)")

            # 查找导航按钮
            button = await self._find_navigation_button(button_type)

            if button:
                try:
                    # 确保按钮在视窗内
                    await button.scroll_into_view_if_needed()
                    await self.page.wait_for_timeout(200)

                    # 点击按钮
                    await button.click()
                    await self.page.wait_for_timeout(800)  # 等待页面更新

                    print(f"✅ 成功点击{direction}月份按钮")

                    # 验证导航是否成功（检查月份是否变化）
                    await self.page.wait_for_timeout(500)

                except Exception as e:
                    print(f"❌ 点击{direction}月份按钮失败: {e}")
                    navigation_success = False
                    break
            else:
                print(f"⚠️ 第{i+1}次无法找到{direction}月份按钮")
                navigation_success = False
                break

        if not navigation_success:
            print(f"❌ 月份导航失败，无法完成{direction}操作")

        return navigation_success
    
    async def _click_year_navigation(self, direction: str, times: int):
        """点击年份导航按钮 - 优化版本"""
        button_type = 'next_year' if direction == 'next' else 'prev_year'
        navigation_success = True

        for i in range(times):
            print(f"🔄 尝试点击年份{direction}按钮 (第{i+1}/{times}次)")

            # 查找导航按钮
            button = await self._find_navigation_button(button_type)

            if button:
                try:
                    # 确保按钮在视窗内
                    await button.scroll_into_view_if_needed()
                    await self.page.wait_for_timeout(200)

                    # 点击按钮
                    await button.click()
                    await self.page.wait_for_timeout(1000)  # 年份切换需要更长等待

                    print(f"✅ 成功点击年份{direction}按钮")

                    # 验证年份导航是否成功
                    await self.page.wait_for_timeout(500)

                except Exception as e:
                    print(f"❌ 点击年份{direction}按钮失败: {e}")
                    navigation_success = False
                    break
            else:
                print(f"⚠️ 第{i+1}次无法找到年份{direction}按钮")
                navigation_success = False
                break

        if not navigation_success:
            print(f"❌ 年份导航失败，无法完成{direction}操作")

        return navigation_success

    async def debug_navigation_buttons(self):
        """调试导航按钮 - 显示所有可用的导航按钮信息"""
        print("🔍 开始调试导航按钮...")

        button_types = ['prev_month', 'next_month', 'prev_year', 'next_year']

        for button_type in button_types:
            print(f"\n--- 检查 {button_type} 按钮 ---")
            button = await self._find_navigation_button(button_type)

            if button:
                try:
                    # 获取按钮的详细信息
                    aria_label = await button.get_attribute('aria-label')
                    button_class = await button.get_attribute('class')
                    is_visible = await button.is_visible()
                    is_enabled = await button.is_enabled()

                    print(f"✅ 找到按钮:")
                    print(f"   aria-label: {aria_label}")
                    print(f"   class: {button_class}")
                    print(f"   visible: {is_visible}")
                    print(f"   enabled: {is_enabled}")

                    # 获取按钮内的SVG信息
                    svg = await button.query_selector('svg')
                    if svg:
                        viewBox = await svg.get_attribute('viewBox')
                        path = await svg.query_selector('path')
                        if path:
                            path_d = await path.get_attribute('d')
                            print(f"   SVG viewBox: {viewBox}")
                            print(f"   SVG path: {path_d}")

                except Exception as e:
                    print(f"⚠️ 获取按钮信息失败: {e}")
            else:
                print(f"❌ 未找到 {button_type} 按钮")

        # 额外调试：显示页面上所有的按钮
        print(f"\n--- 页面上所有按钮信息 ---")
        try:
            all_buttons = await self.page.query_selector_all('button')
            print(f"📊 页面上共有 {len(all_buttons)} 个按钮")

            calendar_buttons = []
            for i, button in enumerate(all_buttons):
                try:
                    aria_label = await button.get_attribute('aria-label')
                    button_text = await button.text_content()

                    # 只显示可能与日历相关的按钮
                    if (aria_label and any(keyword in aria_label for keyword in ['月', '年', 'month', 'year']) or
                        button_text and any(keyword in button_text for keyword in ['月', '年'])):
                        calendar_buttons.append({
                            'index': i,
                            'aria_label': aria_label,
                            'text': button_text.strip() if button_text else '',
                            'visible': await button.is_visible(),
                            'enabled': await button.is_enabled()
                        })
                except Exception as e:
                    continue

            print(f"🗓️ 可能的日历相关按钮 ({len(calendar_buttons)} 个):")
            for btn_info in calendar_buttons:
                print(f"   按钮 {btn_info['index']}: aria-label='{btn_info['aria_label']}', "
                      f"text='{btn_info['text']}', visible={btn_info['visible']}, enabled={btn_info['enabled']}")

        except Exception as e:
            print(f"⚠️ 获取页面按钮信息失败: {e}")

    async def test_navigation_buttons(self):
        """测试导航按钮功能"""
        print("🧪 开始测试导航按钮功能...")

        # 首先显示当前日期
        try:
            current_year, current_month = await self._get_current_displayed_date()
            print(f"📅 当前显示日期: {current_year}年{current_month}月")
        except Exception as e:
            print(f"⚠️ 无法获取当前日期: {e}")
            return

        # 测试每个导航按钮
        test_cases = [
            ('next_month', '下个月'),
            ('prev_month', '上个月'),
            ('next_year', '明年'),
            ('prev_year', '去年')
        ]

        for button_type, description in test_cases:
            print(f"\n--- 测试 {description} 按钮 ---")

            # 获取测试前的日期
            before_year, before_month = await self._get_current_displayed_date()
            print(f"测试前: {before_year}年{before_month}月")

            # 查找并点击按钮
            button = await self._find_navigation_button(button_type)
            if button:
                try:
                    await button.click()
                    await self.page.wait_for_timeout(1000)

                    # 获取测试后的日期
                    after_year, after_month = await self._get_current_displayed_date()
                    print(f"测试后: {after_year}年{after_month}月")

                    # 验证变化
                    if before_year != after_year or before_month != after_month:
                        print(f"✅ {description} 按钮工作正常")
                    else:
                        print(f"⚠️ {description} 按钮点击后日期未变化")

                except Exception as e:
                    print(f"❌ 测试 {description} 按钮失败: {e}")
            else:
                print(f"❌ 未找到 {description} 按钮")

            # 等待一下再进行下一个测试
            await self.page.wait_for_timeout(500)

    async def _select_specific_day(self, year: int, month: int, day: int):
        """选择具体的日期 - 基于新HTML结构优化"""
        target_date_str = f"{year}-{month:02d}-{day:02d}"
        
        try:
            print(f"🗓️ 开始选择具体日期: {year}年{month}月{day}日")
            
            # 基于您提供的HTML结构更新日期选择器
            day_selectors = [
                f'div[data-value="{target_date_str}"][data-reka-calendar-cell-trigger]',  # 精确匹配
                f'div[day="{target_date_str}"]',  # day属性匹配
                f'div[month="{year}-{month:02d}-01"][day="{target_date_str}"]',  # 组合属性匹配
                f'[aria-label*="{year}年{month}月{day}日"]',  # aria-label匹配
                f'div[role="button"][data-value="{target_date_str}"]',  # role + data-value
                f'div[data-reka-calendar-cell-trigger]:has-text("{day}")',  # 含有指定日期文本
            ]
            
            day_element = None
            
            # 方法1: 使用精确选择器
            for selector in day_selectors:
                try:
                    day_element = await self.page.query_selector(selector)
                    if day_element:
                        print(f"✅ 通过选择器找到日期元素: {selector}")
                        break
                except Exception as e:
                    continue
            
            # 方法2: 查找所有日历单元格，然后筛选
            if not day_element:
                print("🔍 使用备用方法查找日期元素...")
                try:
                    # 查找所有日历单元格
                    calendar_cells = await self.page.query_selector_all('div[data-reka-calendar-cell-trigger]')
                    print(f"📊 找到 {len(calendar_cells)} 个日历单元格")
                    
                    for cell in calendar_cells:
                        try:
                            # 检查data-value属性
                            data_value = await cell.get_attribute('data-value')
                            day_attr = await cell.get_attribute('day')
                            aria_label = await cell.get_attribute('aria-label')
                            cell_text = await cell.text_content()
                            
                            # 多重匹配检查
                            if (data_value == target_date_str or 
                                day_attr == target_date_str or
                                (aria_label and f"{year}年{month}月{day}日" in aria_label) or
                                (cell_text and cell_text.strip() == str(day) and 
                                 data_value and data_value.startswith(f"{year}-{month:02d}"))):
                                day_element = cell
                                print(f"✅ 通过属性匹配找到日期元素: {data_value}, {aria_label}")
                                break
                                
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    print(f"⚠️ 日历单元格查找失败: {e}")
            
            # 方法3: 文本匹配 + 父容器验证
            if not day_element:
                print("🔍 使用文本匹配方法...")
                try:
                    all_day_elements = await self.page.query_selector_all('div[role="button"]')
                    for element in all_day_elements:
                        text = await element.text_content()
                        if text and text.strip() == str(day):
                            # 验证是否在正确的月份容器中
                            day_attr = await element.get_attribute('day')
                            month_attr = await element.get_attribute('month')
                            aria_label = await element.get_attribute('aria-label')
                            
                            # 检查月份匹配
                            if (day_attr and target_date_str in day_attr) or \
                               (month_attr and f"{year}-{month:02d}" in month_attr) or \
                               (aria_label and f"{month}月" in aria_label):
                                day_element = element
                                print(f"✅ 通过文本匹配找到日期元素: {text}")
                                break
                                
                except Exception as e:
                    print(f"⚠️ 文本匹配方法失败: {e}")
            
            # 执行点击操作
            if day_element:
                try:
                    # 确保元素可见
                    await day_element.scroll_into_view_if_needed()
                    await self.page.wait_for_timeout(500)
                    
                    # 检查元素状态
                    is_visible = await day_element.is_visible()
                    is_enabled = await day_element.is_enabled()
                    
                    if is_visible and is_enabled:
                        await day_element.click()
                        print(f"✅ 成功点击日期 {day}")
                        await self.page.wait_for_timeout(1500)  # 等待日历关闭
                        
                        # 验证日期选择是否成功
                        try:
                            # 等待一下让日历完全关闭
                            await self.page.wait_for_timeout(1000)
                            
                            # 检查日历是否已关闭或日期是否已选中
                            current_date_button = await self.page.query_selector('button[id*="reka-popover-trigger"]')
                            if current_date_button:
                                button_text = await current_date_button.text_content()
                                print(f"📅 点击后的日期显示: {button_text}")
                                
                                # 更宽松的验证条件
                                if (f"{year}年{month}月" in button_text or 
                                    f"{month}月" in button_text or
                                    str(year) in button_text):
                                    print(f"✅ 日期选择验证成功: {button_text}")
                                else:
                                    print(f"⚠️ 日期选择可能未成功，但继续执行: {button_text}")
                        except Exception as e:
                            print(f"⚠️ 日期验证出错，但继续执行: {e}")
                            
                    else:
                        print(f"❌ 日期元素不可用: visible={is_visible}, enabled={is_enabled}")
                        
                except Exception as e:
                    print(f"❌ 点击日期元素失败: {e}")
            else:
                print(f"❌ 无法找到日期 {day} 的可点击元素")
                
                # 调试信息：显示可用的日期
                try:
                    available_cells = await self.page.query_selector_all('div[data-reka-calendar-cell-trigger]')
                    available_dates = []
                    for cell in available_cells[:10]:  # 只显示前10个
                        try:
                            data_value = await cell.get_attribute('data-value')
                            text = await cell.text_content()
                            if data_value:
                                available_dates.append(f"{text}({data_value})")
                        except:
                            continue
                    print(f"🔍 可用日期示例: {available_dates}")
                except:
                    pass
                
        except Exception as e:
            print(f"❌ 选择具体日期失败: {e}")
            import traceback
            traceback.print_exc()

    def _parse_date_string_enhanced(self, date_str: str) -> Optional[datetime]:
        """增强的日期解析 - 更准确地处理年份推断"""
        try:
            match = re.match(r'(\d+)月(\d+)日', date_str)
            if match:
                month = int(match.group(1))
                day = int(match.group(2))
                
                # 如果设置了目标日期，优先使用目标日期的年份
                if self._target_date:
                    year = self._target_date.year
                    # 检查日期是否合理
                    try:
                        parsed_date = datetime(year, month, day)
                        return parsed_date
                    except ValueError:
                        pass
                
                # 回退到原来的逻辑
                current_year = datetime.now().year
                current_month = datetime.now().month
                
                year = current_year
                if month > current_month:
                    year -= 1
                    
                return datetime(year, month, day)
                
        except Exception as e:
            print(f"解析日期失败: {date_str}, 错误: {e}")
            
        return None
    
    def _create_datetime_from_time_and_date(self, time_str: str, date_str: str) -> Optional[datetime]:
        """根据时间字符串和日期字符串创建完整的datetime对象"""
        try:
            # 解析时间
            time_match = re.match(r'(\d{1,2}):(\d{2})', time_str)
            if not time_match:
                return None
            
            hour = int(time_match.group(1))
            minute = int(time_match.group(2))
            
            # 解析日期
            if date_str:
                base_date = self._parse_date_string_enhanced(date_str)
                if base_date:
                    return datetime(base_date.year, base_date.month, base_date.day, hour, minute)
            
            # 如果没有日期信息，使用目标日期
            if self._target_date:
                return datetime(self._target_date.year, self._target_date.month, 
                              self._target_date.day, hour, minute)
            
            return None
            
        except Exception as e:
            print(f"创建datetime对象失败: {e}")
            return None
    
    async def _extract_time_from_container(self, container) -> str:
        """从新闻容器内部精确提取时间信息 - 基于HTML结构分析优化版"""
        try:
            # 方法1: 基于你提供的HTML结构分析的精确时间提取
            # 时间在 span.text-neutrals-80.text-sm 中
            time_selectors_priority = [
                # 最高优先级：基于你分析的精确结构
                'span.text-neutrals-80.text-sm.font-normal',
                'span.text-neutrals-80.text-sm',
                'span.text-neutrals-80',
                # 备用：原有的选择器
                'span[data-apppush]',
                'time',
                '.time',
            ]

            for selector in time_selectors_priority:
                try:
                    time_element = await container.query_selector(selector)
                    if time_element:
                        time_text = await time_element.text_content()
                        time_text = time_text.strip() if time_text else ""

                        # 精确匹配时间格式 (如 "23:48")
                        if re.match(r'^\d{1,2}:\d{2}$', time_text):
                            return time_text
                        elif time_text and ':' in time_text:
                            time_match = re.search(r'\b(\d{1,2}:\d{2})\b', time_text)
                            if time_match:
                                return time_match.group(1)

                except Exception as e:
                    continue

            # 方法2: 基于容器结构的智能提取
            # 在 border-brand-tertiary.border-l-4 容器内查找第一个时间span
            try:
                # 查找所有span，按DOM顺序，第一个包含时间格式的通常是时间
                all_spans = await container.query_selector_all('span')

                for span in all_spans:
                    try:
                        span_text = await span.text_content()
                        if span_text:
                            span_text = span_text.strip()
                            # 检查是否为时间格式
                            if re.match(r'^\d{1,2}:\d{2}$', span_text):
                                # 验证时间有效性
                                hour, minute = span_text.split(':')
                                if 0 <= int(hour) <= 23 and 0 <= int(minute) <= 59:
                                    return span_text
                    except:
                        continue

            except Exception as e:
                pass

            # 方法3: 从容器全文提取（最后的备用方案）
            try:
                container_text = await container.text_content()
                if container_text:
                    # 更精确的时间模式匹配
                    time_patterns = [
                        r'\b(\d{1,2}:\d{2})\b(?!\d)',  # 避免匹配到更长的数字
                        r'(\d{1,2}:\d{2})',
                    ]

                    for pattern in time_patterns:
                        time_matches = re.findall(pattern, container_text)
                        if time_matches:
                            # 过滤掉明显不是时间的匹配（如比例等）
                            for match in time_matches:
                                hour, minute = match.split(':')
                                if 0 <= int(hour) <= 23 and 0 <= int(minute) <= 59:
                                    return match
            except Exception as e:
                pass

            return ""

        except Exception as e:
            print(f"❌ 容器时间提取失败: {e}")
            return ""
    
    async def _extract_news_urls_from_container(self, container, index: int = 0) -> Optional[str]:
        """从容器中提取新闻URL - 基于HTML结构分析优化版"""
        try:
            # 基于你的分析：标题链接在 a.text-lg.font-medium 中
            title_link_selectors = [
                'a.text-neutrals-80.text-lg.font-medium',  # 基于你的分析
                'a.text-lg.font-medium',                   # 简化版本
                'a[href*="/zh/articles/"]',                # 原有备用
            ]

            title_link = None
            for selector in title_link_selectors:
                title_link = await container.query_selector(selector)
                if title_link:
                    break

            if not title_link:
                return None

            url = await title_link.get_attribute('href')

            if url and url.startswith('/'):
                url = f"https://www.panewslab.com{url}"

            # 检查是否已处理过
            if url in self._processed_urls:
                return None

            return url

        except Exception as e:
            print(f"❌ 容器 {index} 提取URL失败: {e}")
            return None

    async def _extract_news_item(self, container, index: int = 0) -> Optional[NewsItem]:
        """从容器中提取单个新闻项 - 基于HTML结构分析优化版"""
        try:
            # 提取时间
            time_text = await self._extract_time_from_container(container)
            if not time_text:
                return None

            # 提取标题和链接 - 基于你的分析：a.text-lg.font-medium
            title_link_selectors = [
                'a.text-neutrals-80.text-lg.font-medium',  # 基于你的分析
                'a.text-lg.font-medium',                   # 简化版本
                'a[href*="/zh/articles/"]',                # 原有备用
            ]

            title_link = None
            for selector in title_link_selectors:
                title_link = await container.query_selector(selector)
                if title_link:
                    break

            if not title_link:
                return None

            title = (await title_link.text_content()).strip()
            url = await title_link.get_attribute('href')

            if url and url.startswith('/'):
                url = f"https://www.panewslab.com{url}"

            # 检查是否已处理过
            if url in self._processed_urls:
                return None

            # 提取详细内容 - 基于你的分析：div.text-base.font-normal
            content_selectors = [
                'div.text-neutrals-60.text-base.font-normal',  # 基于你的分析
                'div.text-base.font-normal',                   # 简化版本
                '.text-neutrals-60.line-clamp-3',             # 原有备用
            ]

            content = ""
            extracted_date = ""

            for selector in content_selectors:
                content_element = await container.query_selector(selector)
                if content_element:
                    content = (await content_element.text_content()).strip()
                    if content:
                        break

            # 从内容中提取日期
            if content:
                date_match = re.search(r'PANews (\d+月\d+日)消息', content)
                if date_match:
                    extracted_date = date_match.group(1)

            # 检查是否为"首发"新闻 - 基于你的分析：span.text-xs
            is_featured_selectors = [
                'span.text-xs:has-text("首发")',  # 基于你的分析
                'span:has-text("首发")',          # 原有备用
            ]

            is_featured = False
            for selector in is_featured_selectors:
                featured_element = await container.query_selector(selector)
                if featured_element:
                    is_featured = True
                    break

            # 创建完整的datetime对象
            raw_datetime = self._create_datetime_from_time_and_date(time_text, extracted_date)

            # 验证是否属于目标日期
            if self._target_date and raw_datetime:
                if raw_datetime.date() != self._target_date.date():
                    print(f"⚠️ 新闻日期 {raw_datetime.date()} 不匹配目标日期 {self._target_date.date()}，跳过")
                    return None

            # 记录已处理的URL
            if url:
                self._processed_urls.add(url)

            # 创建新闻项
            news_item = NewsItem(
                title=title,
                content=content,
                time=time_text,
                date=extracted_date,
                url=url or "",
                is_featured=is_featured,
                raw_datetime=raw_datetime
            )

            print(f"📰 容器 {index} 提取成功: {time_text} - {title[:50]}...")

            return news_item

        except Exception as e:
            print(f"❌ 容器 {index} 提取新闻项失败: {e}")
            return None
    
    async def _check_date_dividers_in_new_content(self) -> List[str]:
        """检查页面中的日期分隔符"""
        found_dates = []
        
        try:
            month_containers = await self.page.query_selector_all(
                '.bg-brand-primary.flex.items-center.justify-center.px-2.py-1.text-white'
            )
            
            day_containers = await self.page.query_selector_all(
                '.text-brand-primary.flex.items-center.justify-center.px-2.text-xl.font-medium'
            )
            
            for i, month_container in enumerate(month_containers):
                try:
                    month_text = await month_container.text_content()
                    month_text = month_text.strip()
                    
                    if month_text.endswith('月'):
                        month_num = month_text[:-1]
                        
                        if i < len(day_containers):
                            day_container = day_containers[i]
                            day_text = await day_container.text_content()
                            day_text = day_text.strip()
                            
                            if day_text.isdigit():
                                date_str = f"{month_num}月{day_text}日"
                                found_dates.append(date_str)
                                print(f"📅 发现日期分隔符: {date_str}")
                                
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"❌ 检查日期分隔符失败: {e}")
            
        return found_dates
    
    def _should_stop_loading(self, found_dates: List[str], target_date: datetime) -> bool:
        """判断是否应该停止加载"""
        if not found_dates or not target_date:
            return False
            
        for date_str in found_dates:
            parsed_date = self._parse_date_string_enhanced(date_str)
            if parsed_date and parsed_date.date() < target_date.date():
                print(f"🛑 发现日期 {date_str} 早于目标日期，停止加载")
                return True
                
        return False
    
    async def _parse_current_page_news(self) -> List[NewsItem]:
        """解析当前页面的新闻项 - 基于HTML结构分析优化版"""
        news_items = []

        try:
            # 基于你的分析：每条快讯都在 div.border-brand-tertiary.border-l-4 容器内
            news_container_selectors = [
                'div.border-brand-tertiary.border-l-4',  # 基于你的精确分析
                '.border-brand-tertiary',                # 原有备用选择器
            ]

            news_containers = []
            for selector in news_container_selectors:
                news_containers = await self.page.query_selector_all(selector)
                if news_containers:
                    print(f"✅ 使用选择器 '{selector}' 找到 {len(news_containers)} 个新闻容器")
                    break

            if not news_containers:
                print("❌ 未找到任何新闻容器")
                return news_items

            for index, container in enumerate(news_containers):
                news_item = await self._extract_news_item(container, index)
                if news_item:
                    news_items.append(news_item)

        except Exception as e:
            print(f"❌ 解析当前页面新闻失败: {e}")

        return news_items
    

    async def _collect_urls_optimized_method(self) -> List[str]:
        """使用专家建议的优化方法收集新闻URL"""
        urls = []

        try:
            # 等待快讯元素加载
            await self.page.wait_for_selector('a.text-neutrals-80', timeout=10000)

            # 获取所有快讯a标签 - 但要过滤出真正的新闻链接
            news_links = await self.page.query_selector_all('a.text-neutrals-80[href*="/zh/articles/"]')
            print(f"📊 找到 {len(news_links)} 个新闻链接")

            for i, link in enumerate(news_links):
                try:
                    href = await link.get_attribute('href')
                    if href:
                        if href.startswith('/'):
                            href = f"https://www.panewslab.com{href}"

                        # 检查是否已处理过
                        if href not in self._processed_urls:
                            urls.append(href)

                except Exception as e:
                    print(f"⚠️ 处理链接 {i} 失败: {e}")
                    continue

        except Exception as e:
            print(f"❌ 优化方法收集URL失败: {e}")

        return urls

    async def incremental_load_and_parse(self, target_date: Optional[datetime] = None,
                                       max_loads: int = 25) -> List[NewsItem]:
        """增量加载和解析"""
        all_news_items = []
        load_count = 0

        print("🔄 开始增量加载和解析...")

        # 解析初始页面
        initial_items = await self._parse_current_page_news()
        all_news_items.extend(initial_items)
        print(f"📊 初始页面获得 {len(initial_items)} 条新闻")

        while load_count < max_loads:
            try:
                load_more_button = await self.page.query_selector(
                    'button:has-text("加载更多快讯")'
                )

                if not load_more_button:
                    break

                is_visible = await load_more_button.is_visible()
                is_enabled = await load_more_button.is_enabled()

                if not is_visible or not is_enabled:
                    break

                print(f"🔄 第 {load_count + 1} 次点击'加载更多快讯'...")
                await load_more_button.scroll_into_view_if_needed()
                await load_more_button.click()

                await self.page.wait_for_timeout(3000)

                new_items = await self._parse_current_page_news()
                # 只添加新的项目（避免重复）
                new_unique_items = []
                existing_urls = {item.url for item in all_news_items}
                for item in new_items:
                    if item.url not in existing_urls:
                        new_unique_items.append(item)
                        existing_urls.add(item.url)

                all_news_items.extend(new_unique_items)

                if new_unique_items:
                    print(f"✅ 本次加载获得 {len(new_unique_items)} 条新新闻")
                else:
                    print("⚠️ 本次加载未获得新内容")

                if target_date:
                    found_dates = await self._check_date_dividers_in_new_content()
                    if self._should_stop_loading(found_dates, target_date):
                        break

                load_count += 1

                if not new_unique_items:
                    break

           

            except Exception as e:
                print(f"❌ 增量加载失败: {e}")
                break

        # 按时间排序（最新的在前）
        all_news_items.sort(key=lambda x: x.raw_datetime if x.raw_datetime else datetime.min, reverse=True)

        print(f"✅ 增量加载完成！总共获得 {len(all_news_items)} 条新闻")
        return all_news_items
    
    def save_to_csv(self, news_items: List[NewsItem], filename: str = None) -> str:
        """保存新闻数据到CSV文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"panews_date_fixed_{timestamp}.csv"
        
        if not filename.endswith('.csv'):
            filename += '.csv'
        
        filepath = Path(filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                if not news_items:
                    print("❌ 没有新闻数据可保存")
                    return str(filepath)
                
                fieldnames = ['title', 'content', 'time', 'date', 'url', 'is_featured', 'scraped_at']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for item in news_items:
                    row_data = asdict(item)
                    # 移除raw_datetime字段（不需要保存到CSV）
                    row_data.pop('raw_datetime', None)
                    writer.writerow(row_data)
                
            print(f"💾 成功保存 {len(news_items)} 条新闻到 {filepath}")
            return str(filepath)
            
        except Exception as e:
            print(f"❌ 保存CSV文件失败: {e}")
            return ""
    
    async def scrape_news_by_specific_date(self, target_year: int, target_month: int,
                                         target_day: int, max_loads: int = 25,
                                         save_csv: bool = True) -> Tuple[List[NewsItem], str]:
        """按具体日期爬取新闻（修复日期过滤和排序版）"""
        target_date = datetime(target_year, target_month, target_day)
        print(f"🎯 开始爬取 {target_date.strftime('%Y年%m月%d日')} 的新闻...")

        self._processed_urls.clear()

        await self.navigate_to_newsflash()
        await self.select_specific_date(target_year, target_month, target_day)

        all_news_items = await self.incremental_load_and_parse(target_date, max_loads)

        # 严格按目标日期过滤
        filtered_items = []
        for item in all_news_items:
            if item.raw_datetime and item.raw_datetime.date() == target_date.date():
                filtered_items.append(item)
            elif item.date:  # 备用检查
                item_date = self._parse_date_string_enhanced(item.date)
                if item_date and item_date.date() == target_date.date():
                    filtered_items.append(item)

        # 最终排序确保最新的在前
        filtered_items.sort(key=lambda x: x.raw_datetime if x.raw_datetime else datetime.min, reverse=True)

        print(f"🎉 成功爬取目标日期新闻 {len(filtered_items)} 条")

        # 显示前几条新闻验证
        if filtered_items:
            print(f"\n🕐 最新新闻验证（前5条）:")
            for i, item in enumerate(filtered_items[:5]):
                print(f"第{i+1}条: {item.time} - {item.title[:50]}...")

        # 保存到CSV
        csv_path = ""
        if save_csv and filtered_items:
            date_str = target_date.strftime("%Y%m%d")
            filename = f"panews_date_fixed_{date_str}.csv"
            csv_path = self.save_to_csv(filtered_items, filename)

        return filtered_items, csv_path

    async def scrape_news_by_date(self, date_input, max_loads: int = 25, 
                                  save_csv: bool = True) -> Tuple[List[NewsItem], str]:
        """按日期爬取新闻的便捷方法
        
        Args:
            date_input: 日期输入，可以是：
                      - 字符串格式 "YYYY-MM-DD"
                      - datetime.date 对象
                      - datetime.datetime 对象
            max_loads: 最大加载次数
            save_csv: 是否保存为CSV文件
            
        Returns:
            包含新闻列表和CSV文件路径的元组
        """
        from datetime import datetime, date
        
        try:
            # 处理不同类型的日期输入
            if isinstance(date_input, str):
                date_obj = datetime.strptime(date_input, "%Y-%m-%d")
            elif isinstance(date_input, date):
                date_obj = datetime.combine(date_input, datetime.min.time())
            elif isinstance(date_input, datetime):
                date_obj = date_input
            else:
                raise ValueError(f"不支持的日期类型: {type(date_input)}")
                
            return await self.scrape_news_by_specific_date(
                date_obj.year, date_obj.month, date_obj.day, max_loads, save_csv
            )
        except ValueError as e:
            print(f"❌ 日期格式错误: {date_input}, 请使用 YYYY-MM-DD 格式或 date/datetime 对象")
            raise ValueError(f"日期格式错误: {e}")

    @classmethod
    def get_random_user_agent(cls) -> str:
        """获取随机的现代化User-Agent"""
        import random
        
        # 最新的Chrome版本列表 (2025年常见版本)
        chrome_versions = [
            "*********",
            "131.0.0.0", 
            "130.0.0.0",
            "129.0.0.0",
            "128.0.0.0"
        ]
        
        # Windows版本
        windows_versions = [
            "Windows NT 10.0; Win64; x64",
            "Windows NT 11.0; Win64; x64"  # Windows 11
        ]
        
        chrome_version = random.choice(chrome_versions)
        windows_version = random.choice(windows_versions)
        
        return (
            f"Mozilla/5.0 ({windows_version}) AppleWebKit/537.36 "
            f"(KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36"
        )
    
    @classmethod
    def get_mobile_user_agent(cls) -> str:
        """获取移动端User-Agent"""
        return (            "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
        )
    
    async def _verify_timezone_setting(self):
        """验证页面的时区设置是否正确"""
        try:
            # 执行JavaScript来检查时区设置
            timezone_info = await self.page.evaluate("""
                () => {
                    const now = new Date();
                    return {
                        timezoneOffset: now.getTimezoneOffset(),
                        localString: now.toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'}),
                        timezoneString: Intl.DateTimeFormat().resolvedOptions().timeZone,
                        userAgent: navigator.userAgent,
                        language: navigator.language
                    };
                }
            """)
            
            print(f"🕒 时区验证结果:")
            print(f"   - 时区偏移: {timezone_info['timezoneOffset']} 分钟 (期望: -480)")
            print(f"   - 本地时间: {timezone_info['localString']}")
            print(f"   - 时区标识: {timezone_info['timezoneString']}")
            print(f"   - 浏览器语言: {timezone_info['language']}")
            
            if timezone_info['timezoneOffset'] == -480:
                print("✅ 时区设置正确 (UTC+8)")
                return True
            else:
                print("❌ 时区设置不正确，尝试重新注入")
                await self._inject_timezone_script(self.page)
                return False
                
        except Exception as e:
            print(f"⚠️ 时区验证失败: {e}")         
        return False    


# 示例代码：爬取2025-04-20的新闻并保存为CSV文件
if __name__ == "__main__":
    import asyncio
    import csv
    import os
    from datetime import datetime, date
    
    async def main():
        """示例：爬取2025-04-20的新闻数据并保存为CSV文件"""
        
        print("🚀 开始爬取2025-04-20的新闻数据...")
        
        # 使用异步上下文管理器确保浏览器正确启动和关闭
        async with PANewsScraper(
            headless=True,
            timeout=30000,
            performance_mode="balanced",
            use_random_ua=False,
            mobile=False
        ) as scraper:
            
            # 设置目标日期
            target_date = date(2025, 4, 20)
            
            # 执行爬取 (现在支持直接传递date对象)
            all_news, csv_path = await scraper.scrape_news_by_date(target_date)
            
            if not all_news:
                print("❌ 未获取到任何新闻数据")
                return
            
            print(f"✅ 成功爬取到 {len(all_news)} 条新闻")
            
            # 如果已经有CSV文件路径，说明已经自动保存了
            if csv_path:
                print(f"📄 新闻数据已自动保存到: {csv_path}")
                filename = csv_path
            else:
                # 准备CSV数据 - 处理NewsItem对象
                csv_data = []
                for news_item in all_news:
                    # NewsItem是dataclass，可以直接访问属性
                    csv_row = {
                        '标题': news_item.title,
                        '发布时间': news_item.time,
                        '内容': news_item.content,
                        '链接': news_item.url,
                        '来源': 'PANews',
                        '日期': news_item.date,
                        '是否重要': '是' if news_item.is_featured else '否',
                        '爬取时间': news_item.scraped_at or datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    csv_data.append(csv_row)
                
                # 生成文件名
                filename = f"panews_{target_date.strftime('%Y%m%d')}.csv"
                
                # 方法1：使用pandas保存CSV（推荐）
                try:
                    import pandas as pd
                    df = pd.DataFrame(csv_data)
                    df.to_csv(filename, index=False, encoding='utf-8-sig')
                    print(f"📄 新闻数据已保存到: {filename} (使用pandas)")
                except ImportError:
                    print("⚠️ pandas未安装，使用标准csv模块保存")
                    
                    # 方法2：使用标准csv模块
                    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                        if csv_data:
                            fieldnames = csv_data[0].keys()
                            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                            writer.writeheader()
                            writer.writerows(csv_data)
                            print(f"📄 新闻数据已保存到: {filename} (使用csv模块)")
            
            # 显示统计信息
            print(f"\n📊 数据统计:")
            print(f"   - 总计新闻数量: {len(all_news)}")
            if os.path.exists(filename):
                print(f"   - 文件大小: {os.path.getsize(filename) / 1024:.2f} KB")
            print(f"   - 保存格式: CSV (UTF-8 with BOM)")
            
            # 显示前3条新闻的预览
            if all_news:
                print(f"\n📋 前3条新闻预览:")
                for i, news in enumerate(all_news[:3], 1):
                    print(f"   {i}. {news.title[:50]}{'...' if len(news.title) > 50 else ''}")
                    print(f"      时间: {news.time}")
                    print(f"      链接: {news.url}")
                    print()
        
        print("✅ 爬取任务完成！")

    # 运行示例
    print("=" * 60)
    print("PANews爬虫示例 - 爬取2025-04-20新闻数据")
    print("=" * 60)
    
    # 检查必要的模块
    try:
        import pandas as pd
        print("✅ pandas模块可用")
    except ImportError:
        print("⚠️ pandas模块未安装，将使用标准csv模块")
    
    # 运行异步主函数
    asyncio.run(main())
